[package]
name = "tikv"
version = "6.5.0"
authors = ["The TiKV Authors"]
description = "A distributed transactional key-value database powered by Rust and Raft"
license = "Apache-2.0"
keywords = ["KV", "distributed-systems", "raft"]
homepage = "https://tikv.org"
repository = "https://github.com/tikv/tikv/"
readme = "README.md"
edition = "2018"
publish = false

[features]
default = ["test-engine-kv-rocksdb", "test-engine-raft-raft-engine"]
tcmalloc = ["tikv_alloc/tcmalloc"]
jemalloc = ["tikv_alloc/jemalloc", "engine_rocks/jemalloc"]
mimalloc = ["tikv_alloc/mimalloc"]
snmalloc = ["tikv_alloc/snmalloc"]
portable = ["engine_rocks/portable"]
sse = ["engine_rocks/sse"]
mem-profiling = ["tikv_alloc/mem-profiling"]
failpoints = [
  "fail/failpoints",
  "raftstore/failpoints",
  "tikv_util/failpoints",
  "engine_rocks/failpoints",
]

testexport = ["raftstore/testexport", "api_version/testexport", "causal_ts/testexport"]
test-engine-kv-rocksdb = [
  "engine_test/test-engine-kv-rocksdb"
]
test-engine-raft-raft-engine = [
  "engine_test/test-engine-raft-raft-engine"
]
test-engines-rocksdb = [
  "engine_test/test-engines-rocksdb",
]
test-engines-panic = [
  "engine_test/test-engines-panic",
]
cloud-storage-grpc = ["sst_importer/cloud-storage-grpc"]
cloud-storage-dylib = ["sst_importer/cloud-storage-dylib"]
pprof-fp = ["pprof/frame-pointer"]

# for testing configure propegate to other crates
# https://stackoverflow.com/questions/41700543/can-we-share-test-utilites-between-crates
testing = []

[lib]
name = "tikv"

[dependencies]
anyhow = "1.0"
api_version = { workspace = true }
async-stream = "0.2"
async-trait = "0.1"
backtrace = "0.3"
batch-system = { workspace = true }
byteorder = "1.2"
case_macros = { workspace = true }
causal_ts = { workspace = true }
chrono = "0.4"
codec = { workspace = true }
collections = { workspace = true }
concurrency_manager = { workspace = true }
coprocessor_plugin_api = { workspace = true }
crc32fast = "1.2"
crc64fast = "0.1"
crossbeam = "0.8"
dashmap = "5"
encryption_export = { workspace = true }
engine_panic = { workspace = true }
engine_rocks = { workspace = true }
engine_test = { workspace = true }
engine_traits = { workspace = true }
engine_traits_tests = { workspace = true }
error_code = { workspace = true }
fail = "0.5"
file_system = { workspace = true }
flate2 = { version = "1.0", default-features = false, features = ["zlib"] }
futures = { version = "0.3", features = ["thread-pool", "compat"] }
futures-executor = "0.3.1"
futures-timer = "3.0"
futures-util = { version = "0.3.1", default-features = false, features = ["io", "async-await"] }
fxhash = "0.2.1"
getset = "0.1"
grpcio = { workspace = true }
grpcio-health = { workspace = true }
hex = "0.4"
http = "0"
hyper = { version = "0.14", features = ["full"] }
hyper-tls = "0.5"
into_other = { workspace = true }
itertools = "0.10"
keyed_priority_queue = "0.4"
keys = { workspace = true }
kvproto = { workspace = true }
lazy_static = "1.3"
libc = "0.2"
libloading = "0.7"
log = { version = "0.4", features = ["max_level_trace", "release_max_level_debug"] }
log_wrappers = { workspace = true }
match-template = "0.0.1"
memory_trace_macros = { workspace = true }
mime = "0.3.13"
more-asserts = "0.2"
murmur3 = "0.5.1"
nom = { version = "5.1.0", default-features = false, features = ["std"] }
notify = "4"
num-traits = "0.2.14"
num_cpus = "1"
online_config = { workspace = true }
openssl = "0.10"
parking_lot = "0.12"
paste = "1.0"
pd_client = { workspace = true }
pin-project = "1.0"
pnet_datalink = "0.23"
pprof = { version = "0.11", default-features = false, features = ["flamegraph", "protobuf-codec"] }
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
protobuf = { version = "2.8", features = ["bytes"] }
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raft_log_engine = { workspace = true }
raftstore = { workspace = true, features = ["engine_rocks"] }
rand = "0.7.3"
regex = "1.3"
resource_metering = { workspace = true }
rev_lines = "0.2.1"
seahash = "4.1.0"
security = { workspace = true }
semver = "0.11"
serde = { version = "1.0.210", features = ["derive"] }
serde_ignored = "0.1"
serde_json = { version = "1.0", features = ["preserve_order"] }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
smallvec = "1.4"
sst_importer = { workspace = true }
strum = { version = "0.20", features = ["derive"] }
sync_wrapper = "0.1.1"
sysinfo = "0.16"
tempfile = "3.0"
thiserror = "1.0"
tidb_query_aggr = { workspace = true }
tidb_query_common = { workspace = true }
tidb_query_datatype = { workspace = true }
tidb_query_executors = { workspace = true }
tidb_query_expr = { workspace = true }
tikv_alloc = { workspace = true }
tikv_kv = { workspace = true }
tikv_util = { workspace = true }
time = "0.1"
tipb = { workspace = true }
tokio = { version = "1.17", features = ["full"] }
tokio-openssl = "0.6"
tokio-timer = { workspace = true }
toml = "0.5"
tracker = { workspace = true }
txn_types = { workspace = true }
url = "2"
uuid = { version = "0.8.1", features = ["serde", "v4"] }
walkdir = "2"
yatp = { workspace = true }
bytes = "1.10.1"

[dev-dependencies]
api_version = { workspace = true, features = ["testexport"] }
example_coprocessor_plugin = { workspace = true } # should be a binary dependency
hyper-openssl = "0.9"
panic_hook = { workspace = true }
reqwest = { version = "0.11", features = ["blocking"] }
test_sst_importer = { workspace = true }
test_util = { workspace = true }
tokio = { version = "1.17", features = ["macros", "rt-multi-thread", "time"] }
zipf = "6.1.0"

[patch.crates-io]
# TODO: remove this when new raft-rs is published.
raft = { git = "https://github.com/tikv/raft-rs", branch = "master" }
raft-proto = { git = "https://github.com/tikv/raft-rs", branch = "master" }
#protobuf = { git = "https://github.com/pingcap/rust-protobuf", branch = "v2.8" }
#protobuf-codegen = { git = "https://github.com/pingcap/rust-protobuf", branch = "v2.8" }
protobuf-codegen = { git = "https://gitee.com/JJZ921024/rust-protobuf.git" }
protobuf = { git = "https://gitee.com/JJZ921024/rust-protobuf.git" }

# TODO: remove this replacement after rusoto_s3 truly supports virtual-host style (https://github.com/rusoto/rusoto/pull/1823).
rusoto_core = { git = "https://github.com/tikv/rusoto", branch = "gh1482-s3-addr-styles" }
rusoto_credential = { git = "https://github.com/tikv/rusoto", branch = "gh1482-s3-addr-styles" }
rusoto_kms = { git = "https://github.com/tikv/rusoto", branch = "gh1482-s3-addr-styles" }
rusoto_mock = { git = "https://github.com/tikv/rusoto", branch = "gh1482-s3-addr-styles" }
rusoto_s3 = { git = "https://github.com/tikv/rusoto", branch = "gh1482-s3-addr-styles" }
rusoto_sts = { git = "https://github.com/tikv/rusoto", branch = "gh1482-s3-addr-styles" }

snappy-sys = { git = "https://github.com/busyjay/rust-snappy.git", branch = "static-link" }

# remove this when https://github.com/danburkert/fs2-rs/pull/42 is merged.
fs2 = { git = "https://github.com/tabokie/fs2-rs", branch = "tikv" }

# Remove this when a new version is release. We need to solve rust-lang/cmake-rs#143.
cmake = { git = "https://github.com/rust-lang/cmake-rs" }

# TODO: remove this after crossbeam-deque is updated to the next release version.
# This is a workaround for cargo can't resolving the this patch in yatp.
crossbeam-deque = { git = "https://github.com/crossbeam-rs/crossbeam", rev = "41ed3d948720f26149b2ebeaf58fe8a193134056" }

[target.'cfg(target_os = "linux")'.dependencies]
procinfo = { git = "https://github.com/tikv/procinfo-rs", rev = "6599eb9dca74229b2c1fcc44118bef7eff127128" }
# When you modify TiKV cooperatively with kvproto, this will be useful to submit the PR to TiKV and the PR to
# kvproto at the same time.
# After the PR to kvproto is merged, remember to comment this out and run `cargo update -p kvproto`.
# [patch.'https://github.com/pingcap/kvproto']
# kvproto = { git = "https://github.com/your_github_id/kvproto", branch = "your_branch" }

[workspace]
# See https://github.com/rust-lang/rfcs/blob/master/text/2957-cargo-features2.md
# Without resolver = 2, using `cargo build --features x` to build `cmd`
# will _not_ propagate the feature `x` into `cmd`'s direct dependencies.
resolver = "2"
members = [
  "cmd/tikv-ctl",
  "cmd/tikv-server",
  "components/api_version",
  "components/backup",
  "components/backup-stream",
  "components/batch-system",
  "components/case_macros",
  "components/causal_ts",
  "components/cdc",
  "components/cloud",
  "components/codec",
  "components/collections",
  "components/concurrency_manager",
  "components/coprocessor_plugin_api",
  "components/encryption",
  "components/encryption/export",
  "components/engine_rocks_helper",
# Only enable tirocks in local development, otherwise it can slow down compilation.
# TODO: always enable tirocks and remove engine_rocks.
  "components/engine_tirocks",
  "components/error_code",
  "components/external_storage",
  "components/external_storage/export",
  "components/file_system",
  "components/into_other",
  "components/keys",
  "components/log_wrappers",
  "components/online_config",
  "components/panic_hook",
  "components/pd_client",
  "components/profiler",
  "components/raftstore",
  "components/raftstore-v2",
  "components/resolved_ts",
  "components/resource_metering",
  "components/security",
  "components/server",
  "components/snap_recovery",
  "components/sst_importer",
  "components/test_backup",
  "components/test_coprocessor",
  "components/test_coprocessor_plugin/example_plugin",
  "components/test_pd",
  "components/test_pd_client",
  "components/test_raftstore",
  "components/test_sst_importer",
  "components/test_storage",
  "components/test_util",
  "components/tidb_query_aggr",
  "components/tidb_query_codegen",
  "components/tidb_query_common",
  "components/tidb_query_datatype",
  "components/tidb_query_executors",
  "components/tidb_query_expr",
  "components/tikv_alloc",
  "components/tikv_kv",
  "components/tikv_util",
  "components/tipb_helper",
  "components/tracker",
  "components/txn_types",
  "fuzz",
  "fuzz/fuzzer-afl",
  "fuzz/fuzzer-honggfuzz",
  "fuzz/fuzzer-libfuzzer",
  "tests",
]
default-members = ["cmd/tikv-server"]

[workspace.dependencies]
api_version = { path = "components/api_version" }
backup = { path = "components/backup", default-features = false }
backup-stream = { path = "components/backup-stream", default-features = false }
batch-system = { path = "components/batch-system" }
case_macros = { path = "components/case_macros" }
causal_ts = { path = "components/causal_ts" }
cdc = { path = "components/cdc", default-features = false }
cloud = { path = "components/cloud" }
codec = { path = "components/codec" }
collections = { path = "components/collections" }
concurrency_manager = { path = "components/concurrency_manager" }
coprocessor_plugin_api = { path = "components/coprocessor_plugin_api" }
encryption = { path = "components/encryption" }
encryption_export = { path = "components/encryption/export" }
engine_panic = { path = "components/engine_panic" }
engine_rocks = { path = "components/engine_rocks" }
engine_rocks_helper = { path = "components/engine_rocks_helper" }
engine_test = { path = "components/engine_test", default-features = false }
engine_traits = { path = "components/engine_traits" }
engine_traits_tests = { path = "components/engine_traits_tests", default-features = false }
error_code = { path = "components/error_code" }
external_storage = { path = "components/external_storage" }
external_storage_export = { path = "components/external_storage/export" }
file_system = { path = "components/file_system" }
into_other = { path = "components/into_other" }
keys = { path = "components/keys" }
log_wrappers = { path = "components/log_wrappers" }
memory_trace_macros = { path = "components/memory_trace_macros" }
online_config = { path = "components/online_config" }
panic_hook = { path = "components/panic_hook" }
pd_client = { path = "components/pd_client" }
profiler = { path = "components/profiler" }
raft_log_engine = { path = "components/raft_log_engine" }
raftstore = { path = "components/raftstore", default-features = false }
raftstore_v2 = { path = "components/raftstore-v2", default-features = false }
resolved_ts = { path = "components/resolved_ts" }
resource_metering = { path = "components/resource_metering" }
security = { path = "components/security" }
server = { path = "components/server" }
snap_recovery = { path = "components/snap_recovery" }
sst_importer = { path = "components/sst_importer" }
test_backup = { path = "components/test_backup" }
test_coprocessor = { path = "components/test_coprocessor", default-features = false }
example_coprocessor_plugin = { path = "components/test_coprocessor_plugin/example_plugin" }
test_pd = { path = "components/test_pd" }
test_pd_client = { path = "components/test_pd_client" }
test_raftstore = { path = "components/test_raftstore", default-features = false }
test_sst_importer = { path = "components/test_sst_importer" }
test_storage = { path = "components/test_storage", default-features = false }
test_util = { path = "components/test_util" }
tidb_query_aggr = { path = "components/tidb_query_aggr" }
tidb_query_codegen = { path = "components/tidb_query_codegen" }
tidb_query_common = { path = "components/tidb_query_common" }
tidb_query_datatype = { path = "components/tidb_query_datatype" }
tidb_query_executors = { path = "components/tidb_query_executors" }
tidb_query_expr = { path = "components/tidb_query_expr" }
tikv = { path = ".", default-features = false }
tikv_alloc = { path = "components/tikv_alloc" }
tikv_kv = { path = "components/tikv_kv", default-features = false }
tikv_util = { path = "components/tikv_util" }
tipb_helper = { path = "components/tipb_helper" }
tracker = { path = "components/tracker" }
txn_types = { path = "components/txn_types" }
# External libs
grpcio = { version = "0.10.4", default-features = false, features = ["openssl-vendored", "protobuf-codec", "nightly"] }
grpcio-health = { version = "0.10.4", default-features = false, features = ["protobuf-codec"] }
tipb = { git = "https://github.com/pingcap/tipb.git" }
#kvproto = { git = "https://github.com/pingcap/kvproto.git" }
kvproto = { path = "../kvproto", default-features = false, features = ["protobuf-codec"] }
yatp = { git = "https://github.com/tikv/yatp.git", branch = "master" }
tokio-timer = { git = "https://github.com/tikv/tokio", branch = "tokio-timer-hotfix" }

[profile.dev.package.grpcio-sys]
debug = false
opt-level = 1

[profile.dev.package.librocksdb_sys]
debug = false
opt-level = 1

[profile.dev.package.libtitan_sys]
debug = false
opt-level = 1

[profile.dev.package.tirocks-sys]
debug = false
opt-level = 1

[profile.dev.package.tests]
debug = 1
opt-level = 1

[profile.dev]
opt-level = 0
debug = 0
codegen-units = 4
lto = false
incremental = true
panic = 'unwind'
debug-assertions = true
overflow-checks = false
rpath = false

[profile.release]
opt-level = 0
debug = true
codegen-units = 1
lto = "thin"
incremental = false
panic = 'unwind'
debug-assertions = false
overflow-checks = false
rpath = false

[profile.release.package.server]
opt-level = 1
codegen-units = 4

[profile.test]
opt-level = 0
debug = 0
codegen-units = 16
lto = false
incremental = true
debug-assertions = true
overflow-checks = true
rpath = false

# The benchmark profile is identical to release, except that lto = false
[profile.bench]
opt-level = 3
debug = false
codegen-units = 1
lto = 'thin'
incremental = false
debug-assertions = false
overflow-checks = false
rpath = false
