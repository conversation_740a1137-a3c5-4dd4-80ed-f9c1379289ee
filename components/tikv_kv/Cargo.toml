[package]
name = "tikv_kv"
version = "0.1.0"
authors = ["The TiKV Authors"]
description = "The key-value abstraction directly used by TiKV"
edition = "2018"
publish = false

[features]
default = ["test-engine-kv-rocksdb", "test-engine-raft-raft-engine"]
failpoints = ["fail/failpoints"]

test-engine-kv-rocksdb = [
  "raftstore/test-engine-kv-rocksdb"
]
test-engine-raft-raft-engine = [
  "raftstore/test-engine-raft-raft-engine"
]

test-engines-rocksdb = [
  "raftstore/test-engines-rocksdb",
]
test-engines-panic = [
  "raftstore/test-engines-panic",
]

[dependencies]
backtrace = "0.3"
collections = { workspace = true }
engine_panic = { workspace = true }
engine_rocks = { workspace = true }
engine_test = { workspace = true }
engine_traits = { workspace = true }
error_code = { workspace = true }
fail = "0.5"
file_system = { workspace = true }
futures = { version = "0.3", features = ["thread-pool", "compat"] }
into_other = { workspace = true }
kvproto = { workspace = true }
log_wrappers = { workspace = true }
pd_client = { workspace = true }
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raftstore = { workspace = true }
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog_derive = "0.2"
tempfile = "3.0"
thiserror = "1.0"
tikv_util = { workspace = true }
tracker = { workspace = true }
txn_types = { workspace = true }

[dev-dependencies]
keys = { workspace = true }
panic_hook = { workspace = true }
