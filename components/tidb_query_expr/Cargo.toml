[package]
name = "tidb_query_expr"
version = "0.0.1"
edition = "2018"
publish = false
description = "Vector expressions of query engine to run TiDB pushed down executors"

[dependencies]
base64 = "0.13"
bstr = "0.2.8"
byteorder = "1.2"
codec = { workspace = true }
file_system = { workspace = true }
flate2 = { version = "=1.0.11", default-features = false, features = ["zlib"] }
hex = "0.4"
log_wrappers = { workspace = true }
match-template = "0.0.1"
num = { version = "0.3", default-features = false }
num-traits = "0.2"
openssl = { version = "0.10" }
protobuf = "2"
rand = "0.8.3"
regex = "1.1"
safemem = { version = "0.3", default-features = false }
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0"
static_assertions = { version = "1.0", features = ["nightly"] }
tidb_query_codegen = { workspace = true }
tidb_query_common = { workspace = true }
tidb_query_datatype = { workspace = true }
tikv_util = { workspace = true }
time = "0.1"
tipb = { workspace = true }
twoway = "0.2.0"
uuid = { version = "0.8.1", features = ["v4"] }

[dev-dependencies]
bstr = "0.2.8"
chrono = "0.4"
panic_hook = { workspace = true }
profiler = { workspace = true }
tipb_helper = { workspace = true }
