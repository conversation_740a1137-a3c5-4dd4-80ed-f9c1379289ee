[package]
name = "tikv_util"
version = "0.1.0"
edition = "2018"
publish = false

[features]
failpoints = ["fail/failpoints"]
test-cgroup = []

[dependencies]
async-speed-limit = "0.4.0"
backtrace = "0.3.9"
byteorder = "1.2"
bytes = "1.0"
chrono = "0.4"
codec = { workspace = true }
collections = { workspace = true }
cpu-time = "1.0.0"
crc32fast = "1.2"
crossbeam = "0.8"
derive_more = "0.99.3"
error_code = { workspace = true }
fail = "0.5"
futures = { version = "0.3", features = ["compat", "thread-pool"] }
futures-util = { version = "0.3", default-features = false, features = ["io"] }
grpcio = { workspace = true }
http = "0.2.0"
kvproto = { workspace = true }
lazy_static = "1.3"
libc = "0.2"
log = { version = "0.4", features = ["max_level_trace", "release_max_level_debug"] }
log_wrappers = { workspace = true }
mnt = "0.3.1"
nix = "0.24"
num-traits = "0.2"
num_cpus = "1"
online_config = { workspace = true }
openssl = "0.10"
pin-project = "1.0"
prometheus = { version = "0.13", features = ["nightly"] }
prometheus-static-metric = "0.5"
protobuf = "2"
rand = "0.8"
rusoto_core = "0.46.0"
serde = { version = "1.0.210", features = ["derive"] }
serde_json = "1.0"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-async = "2.3"
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
slog-json = "2.3"
slog-term = "2.4"
sysinfo = "0.16"
thiserror = "1.0"
tikv_alloc = { workspace = true }
time = "0.1"
tokio = { version = "1.5", features = ["rt-multi-thread"] }
tokio-executor = "0.1"
tokio-timer = { workspace = true }
tracker = { workspace = true }
url = "2"
yatp = { workspace = true }

[target.'cfg(target_os = "linux")'.dependencies]
procinfo = { git = "https://github.com/tikv/procinfo-rs", rev = "6599eb9dca74229b2c1fcc44118bef7eff127128" }
page_size = "0.4"
procfs = { version = "0.12", default-features = false }

[dev-dependencies]
gag = "1.0"
panic_hook = { workspace = true }
protobuf = "2"
regex = "1.0"
tempfile = "3.0"
toml = "0.5"
utime = "0.2"

[[bench]]
name = "channel"
path = "benches/channel/mod.rs"
test = true
