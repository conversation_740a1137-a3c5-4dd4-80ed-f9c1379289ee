[package]
name = "txn_types"
version = "0.1.0"
edition = "2018"
publish = false

[dependencies]
bitflags = "1.0.1"
byteorder = "1.2"
codec = { workspace = true }
collections = { workspace = true }
error_code = { workspace = true }
farmhash = "1.1.5"
kvproto = { workspace = true }
log_wrappers = { workspace = true }
slog = "2.3"
thiserror = "1.0"
tikv_alloc = { workspace = true }
tikv_util = { workspace = true }

[dev-dependencies]
panic_hook = { workspace = true }
rand = "0.8"
