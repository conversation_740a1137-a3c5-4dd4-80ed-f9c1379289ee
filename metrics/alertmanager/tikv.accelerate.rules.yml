groups:
  - name: tikv_accelerate
    rules:
    - record: tikv_grpc_msg_duration_seconds:p99:1m
      expr: histogram_quantile(0.99, sum(rate(tikv_grpc_msg_duration_seconds_bucket{instance=~".*", type!="kv_gc"}[1m])) by (le, type))
    - record: tikv_raftstore_event_duration_bucket:p99:1m
      expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_event_duration_bucket{instance=~".*"}[1m])) by (le, type))
    - record: tikv_thread_cpu_seconds:1m
      expr: sum(rate(tikv_thread_cpu_seconds_total{instance=~".*"}[1m])) by (instance)
    - record: tikv_raftstore_append_log_duration_seconds:p99:1m
      expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{instance=~".*"}[1m])) by (le, instance))
    - record: tikv_raftstore_raft_process_duration_secs:p99:1m
      expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_raft_process_duration_secs_bucket{instance=~".*", type='ready'}[1m])) by (le, instance))
    - record: tikv_raftstore_request_wait_time_duration_secs:byins:p99:1m
      expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_request_wait_time_duration_secs_bucket{instance=~".*"}[1m])) by (le, instance))
    - record: tikv_raftstore_append_log_duration_seconds:p95:1m
      expr: histogram_quantile(0.95, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{instance=~".*"}[1m])) by (le))
    - record: tikv_raftstore_apply_wait_time_duration_secs:byins:p99:1m
      expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_apply_wait_time_duration_secs_bucket{instance=~".*"}[1m])) by (le, instance))
    - record: tikv_raftstore_apply_log_duration_seconds:p99:1m
      expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_apply_log_duration_seconds_bucket{instance=~".*"}[1m])) by (le, instance))
    - record: tikv_raftstore_request_wait_time_duration_secs:p99:1m
      expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_request_wait_time_duration_secs_bucket{instance=~".*"}[1m])) by (le))
    - record: tikv_raftstore_request_wait_time_duration_secs:p95:1m
      expr: histogram_quantile(0.95, sum(rate(tikv_raftstore_request_wait_time_duration_secs_bucket{instance=~".*"}[1m])) by (le))
    - record: tikv_worker_handled_task:1m
      expr: sum(rate(tikv_worker_handled_task_total{instance=~".*"}[1m])) by (name)
    - record: tikv_engine_num_files_at_level:kv:avg
      expr: avg(tikv_engine_num_files_at_level{instance=~".*", db="kv"}) by (cf, level)
    - record: tikv_engine_num_files_at_level:raft:avg
      expr: avg(tikv_engine_num_files_at_level{instance=~".*", db="raft"}) by (cf, level)
    - record: tikv_pd_request_duration_seconds:avg:1m
      expr: sum(rate(tikv_pd_request_duration_seconds_sum{instance=~".*"}[1m])) by (type) / sum(rate(tikv_pd_request_duration_seconds_count{instance=~".*"}[1m])) by (type)
    - record: tikv_coprocessor_request_wait_seconds:p95:1m
      expr: histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_wait_seconds_bucket{instance=~".*"}[1m])) by (le, instance,req))
    - record: tikv_grpc_msg_duration_seconds:avg:1m
      expr: sum(rate(tikv_grpc_msg_duration_seconds_sum{instance=~".*"}[1m])) by (type) / sum(rate(tikv_grpc_msg_duration_seconds_count[1m])) by (type)
    - record: tikv_raftstore_apply_wait_time_duration_secs:p99:1m
      expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_apply_wait_time_duration_secs_bucket{instance=~".*"}[1m])) by (le))
    - record: tikv_raftstore_apply_wait_time_duration_secs:p95:1m
      expr: histogram_quantile(0.95, sum(rate(tikv_raftstore_apply_wait_time_duration_secs_bucket{instance=~".*"}[1m])) by (le))
    - record: tikv_grpc_msg_duration_seconds:1m
      expr: sum(rate(tikv_grpc_msg_duration_seconds_count{instance=~".*", type!="kv_gc"}[1m])) by (instance,type)
    - record: tikv_raftstore_snapshot_duration_seconds:p99:1m
      expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_snapshot_duration_seconds_bucket{instance=~".*", type="apply"}[1m])) by (le))
    - record: tikv_worker_pending_task:sum
      expr: sum(tikv_worker_pending_task_total{instance=~".*"}) by (name)
    - record: tikv_coprocessor_request_duration_seconds:1m
      expr: sum(rate(tikv_coprocessor_request_duration_seconds_bucket{instance=~".*"}[1m])) by (le)
    - record: tikv_futurepool_pending_task:1m
      expr: sum(rate(tikv_futurepool_pending_task_total{instance=~".*"}[1m])) by (name)
    - record: tikv_storage_engine_async_request:1m
      expr: sum(rate(tikv_storage_engine_async_request_total{instance=~".*", status!~"all|success"}[1m])) by (status)
    - record: tikv_thread_cpu_seconds_nogrpc:1m
      expr: sum(rate(tikv_thread_cpu_seconds_total{instance=~".*", name=~"grpc.*"}[1m])) by (instance)
