[package]
name = "tests"
version = "0.0.1"
edition = "2018"
publish = false

[[test]]
name = "failpoints"
path = "failpoints/mod.rs"
required-features = ["failpoints", "testexport"]

[[test]]
name = "integrations"
path = "integrations/mod.rs"
required-features = ["testexport"]

[[bench]]
name = "raftstore"
harness = false
path = "benches/raftstore/mod.rs"

[[bench]]
name = "coprocessor_executors"
harness = false
path = "benches/coprocessor_executors/mod.rs"

[[bench]]
name = "hierarchy"
harness = false
path = "benches/hierarchy/mod.rs"

[[bench]]
name = "misc"
path = "benches/misc/mod.rs"
test = true

[[bench]]
name = "deadlock_detector"
harness = false
path = "benches/deadlock_detector/mod.rs"

[features]
default = ["failpoints", "testexport", "test-engine-kv-rocksdb", "test-engine-raft-raft-engine"]
failpoints = ["fail/failpoints", "tikv/failpoints", "pd_client/failpoints"]
testexport = ["raftstore/testexport", "tikv/testexport", "pd_client/testexport"]
profiling = ["profiler/profiling"]

test-engine-kv-rocksdb = [
  "raftstore/test-engine-kv-rocksdb"
]
test-engine-raft-raft-engine = [
  "raftstore/test-engine-raft-raft-engine"
]
test-engines-rocksdb = [
  "raftstore/test-engines-rocksdb",
]
test-engines-panic = [
  "raftstore/test-engines-panic",
]
jemalloc = ["tikv/jemalloc"]
mimalloc = ["tikv/mimalloc"]
snmalloc = ["tikv/snmalloc"]
mem-profiling = ["tikv/mem-profiling"]
sse = ["tikv/sse"]
portable = ["tikv/portable"]

[dependencies]
api_version = { workspace = true }
async-trait = "0.1"
batch-system = { workspace = true }
cdc = { workspace = true }
collections = { workspace = true }
crc64fast = "0.1"
crossbeam = "0.8"
encryption = { workspace = true }
engine_rocks_helper = { workspace = true }
error_code = { workspace = true }
fail = "0.5"
file_system = { workspace = true }
futures = "0.3"
grpcio = { workspace = true }
grpcio-health = { version = "0.10", default-features = false }
kvproto = { workspace = true }
libc = "0.2"
log_wrappers = { workspace = true }
more-asserts = "0.2"
online_config = { workspace = true }
paste = "1.0"
pd_client = { workspace = true }
protobuf = { version = "2.8", features = ["bytes"] }
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raft_log_engine = { workspace = true }
raftstore = { workspace = true }
rand = "0.8.3"
slog = { version = "2.3", features = ["max_level_trace", "release_max_level_debug"] }
slog-global = { version = "0.1", git = "https://github.com/breeswish/slog-global.git", rev = "d592f88e4dbba5eb439998463054f1a44fbf17b9" }
tempfile = "3.0"
tidb_query_aggr = { workspace = true }
tidb_query_common = { workspace = true }
tidb_query_datatype = { workspace = true }
tidb_query_executors = { workspace = true }
tidb_query_expr = { workspace = true }
tikv = { workspace = true }
tikv_util = { workspace = true }
time = "0.1"
tipb = { workspace = true }
toml = "0.5"
txn_types = { workspace = true }
uuid = { version = "0.8.1", features = ["serde", "v4"] }

[target.'cfg(target_os = "linux")'.dependencies]
procinfo = { git = "https://github.com/tikv/procinfo-rs", rev = "6599eb9dca74229b2c1fcc44118bef7eff127128" }

[dev-dependencies]
arrow = "13.0"
byteorder = "1.2"
# See https://bheisler.github.io/criterion.rs/book/user_guide/known_limitations.html for the usage
# of `real_blackbox` feature.
causal_ts = { workspace = true }
concurrency_manager = { workspace = true }
criterion = "0.3"
criterion-cpu-time = "0.1"
engine_rocks = { workspace = true }
engine_test = { workspace = true }
engine_traits = { workspace = true }
external_storage_export = { workspace = true }
file_system = { workspace = true }
hyper = { version = "0.14", default-features = false, features = ["runtime"] }
keys = { workspace = true }
panic_hook = { workspace = true }
profiler = { workspace = true }
rand_xorshift = "0.3"
resource_metering = { workspace = true }
security = { workspace = true }
serde_json = "1.0"
sst_importer = { workspace = true }
test_backup = { workspace = true }
test_coprocessor = { workspace = true }
test_pd = { workspace = true }
test_pd_client = { workspace = true }
test_raftstore = { workspace = true }
test_sst_importer = { workspace = true }
test_storage = { workspace = true }
test_util = { workspace = true }
tidb_query_datatype = { workspace = true }
tikv_kv = { workspace = true }
tipb_helper = { workspace = true }
tokio = { version = "1.5", features = ["rt-multi-thread"] }

[target.'cfg(all(target_os = "linux", target_arch = "x86_64"))'.dev-dependencies]
criterion-perf-events = "0.1"
perfcnt = "0.7"
