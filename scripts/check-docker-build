#!/usr/bin/env bash
# This script checks if all cargo targets have path specifications.
set -euo pipefail

for i in $(git ls-files | grep 'Cargo.toml' | grep -v 'fuzz/\|./profiler/'); do
    for target in "test" "bench" "bin" "example"; do
        # from "[[test]]" to the first trailing empty line
        matches=$(sed -n "/\[\[$target\]\]/,/^$/ p" $i)
        # check equal amount of "[[test]]" and "path ="
        if [ $(echo "$matches" | grep -c "[[$target]]") != $(echo "$matches" | grep -c "^path =") ]; then
            echo "Path has not been specified for a $target target in $i, this will break docker build."
            exit 1
        fi
    done
done

echo "Docker build check passed."
