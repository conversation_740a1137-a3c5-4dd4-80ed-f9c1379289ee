use std::{
    convert::{<PERSON><PERSON><PERSON>, TryInto},
    sync::atomic::{AtomicU64, Ordering},
    time::{SystemTime, UNIX_EPOCH},
};

use crate::redis::{RedisType, error::RedisError};

const METADATA_TYPE_MASK: u8 = 0x0f;
const METADATA_ENCODING_MASK: u8 = 0x80;
// 52 bit for microseconds and 11 bit for counter
const VERSION_COUNTER_BITS: u32 = 11;
// global version counter
// it is used to generate unique version for each metadata
static VERSION_COUNTER: AtomicU64 = AtomicU64::new(0);

#[derive(Default)]
pub struct MetaData {
    flag: u8,

    expire: u64,

    version: u64,

    size: u64,
}

impl MetaData {

    pub fn new(redis_type: RedisType, gen_ver: bool) -> Self {
        let version = if gen_ver { generate_version() } else { 0 };
        let flag = METADATA_ENCODING_MASK | redis_type as u8 & METADATA_TYPE_MASK;
        Self {
            flag,
            expire: 0,
            version,
            size: 0,
        }
    }

    pub fn parse(redis_type: RedisType, bytes: &mut Vec<u8>) -> Result<Self, RedisError> {
        if bytes.len() < 9 {
            return Err(RedisError::MetaDataErr);
        }

        let flag = bytes[0];
        let expire = u64::from_le_bytes(bytes[1..9].try_into().unwrap());
        bytes.drain(0..9);

        let metadata = MetaData {
            flag,
            expire,
            version: 0,
            size: 0,
        };

        if redis_type != metadata.redis_type() {
            return Err(RedisError::WrongType);
        }
        
        Ok(metadata)
    }

    pub fn encode(&self, value: &[u8]) -> Vec<u8> {
        let mut bytes = Vec::with_capacity(9);
        bytes.push(self.flag);
        bytes.extend_from_slice(&self.expire.to_le_bytes());
        bytes.extend_from_slice(value);
        bytes
    }

    /// key是否过期
    pub fn is_expired(&self) -> bool {
        if self.expire == 0 {
            return false;
        }
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Time went backwards")
            .as_millis() as u64;
        now >= self.expire
    }

    /// 返回key的ttl ms
    pub fn ttl(&self) -> i64 {
        if self.expire == 0 {
            return -1;
        }
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Time went backwards")
            .as_millis() as u64;
        if now >= self.expire {
            return 0;
        }
        (self.expire - now) as i64
    }

    /// 返回key的过期时间 ms
    pub fn get_expire(&self) -> u64 {
        self.expire
    }

    pub fn set_expire(&mut self, expire: u64) {
        self.expire = expire;
    }

    pub fn version(&self) -> u64 {
        self.version
    }

    pub fn size(&self) -> u64 {
        self.size
    }

    pub fn redis_type(&self) -> RedisType {
        RedisType::try_from(self.flag & METADATA_TYPE_MASK).unwrap()
    }

}

fn generate_version() -> u64 {
    let now = SystemTime::now();
    let duration = now.duration_since(UNIX_EPOCH).expect("Time went backwards");
    let timestamp = duration.as_secs() * 1_000_000 + u64::from(duration.subsec_micros());
    let counter = VERSION_COUNTER.fetch_add(1, Ordering::Relaxed);
    (timestamp << VERSION_COUNTER_BITS) + (counter % (1 << VERSION_COUNTER_BITS))
}
