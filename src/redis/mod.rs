use std::{convert::TryFrom, slice::Iter};

use anyhow::Result;
use chrono::Utc;
use kvproto::kvrpcpb::{ContentType, RedisResponse};

use crate::redis::{error::RedisError, parser::CommandParser};

pub mod cmd;
pub mod error;
pub mod meta;
pub mod parser;

// keyspace id must be 1
pub const REDIS_META_KEY_PREFIX: &[u8] = &[b'r', 0x00, 0x00, 0x01];

#[allow(dead_code)]
pub struct CmdAttr {
    name: &'static str,

    // 命令参数个数, 正数表示需等于n, 负数表示需大于等于n
    arity: i32,

    // index of the first key in command tokens
    // 0 stands for no key, since the first index of command arguments is command name
    first_key: i32,

    // index of the last key in command tokens
    // in normal one-key commands, first key and last key index are both 1
    // -n stands for the n-th last index of the sequence, i.e. args.size() - n
    last_key: i32,

    // step length of key position
    // e.g. key step 2 means "key other key other ..." sequence
    key_step: i32,
}

impl CmdAttr {
    pub const fn new(name: &'static str, arity: i32) -> Self {
        Self {
            name,
            arity,
            first_key: 1,
            last_key: 1,
            key_step: 1,
        }
    }

    // 返回false表示校验失败
    pub fn check_arity(&self, cmd_size: i32) -> bool {
        (self.arity <= 0 || cmd_size == self.arity) && (self.arity >= 0 || cmd_size >= -self.arity)
    }
}

#[repr(u8)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum RedisType {
    None = 0,
    String = 1,
    Hash = 2,
    List = 3,
    Set = 4,
    ZSet = 5,
    Bitmap = 6,
}

impl TryFrom<u8> for RedisType {
    type Error = crate::redis::error::RedisError;

    fn try_from(value: u8) -> Result<Self, Self::Error> {
        match value {
            0 => Ok(RedisType::None),
            1 => Ok(RedisType::String),
            2 => Ok(RedisType::Hash),
            3 => Ok(RedisType::List),
            4 => Ok(RedisType::Set),
            5 => Ok(RedisType::ZSet),
            6 => Ok(RedisType::Bitmap),
            _ => Err(crate::redis::error::RedisError::InvalidArg),
        }
    }
}

pub fn parse_expire_flags(
    parser: &mut CommandParser<Iter<Vec<u8>>>,
    flag: &mut String,
) -> Result<Option<u64>, RedisError> {
    if parser.eat_eq_icase_flag("EX", flag) {
        Ok(Some(
            (parser.take_number::<i64>()? * 1000 + Utc::now().timestamp_millis()) as u64,
        ))
    } else if parser.eat_eq_icase_flag("EXAT", flag) {
        Ok(Some((parser.take_number::<i64>()? * 1000) as u64))
    } else if parser.eat_eq_icase_flag("PX", flag) {
        Ok(Some(
            (parser.take_number::<i64>()? + Utc::now().timestamp_millis()) as u64,
        ))
    } else if parser.eat_eq_icase_flag("PXAT", flag) {
        Ok(Some(parser.take_number::<i64>()? as u64))
    } else {
        Ok(None)
    }
}

impl From<RedisError> for RedisResponse {
    fn from(err: RedisError) -> Self {
        let mut resp = RedisResponse::new();
        resp.set_type(ContentType::Error);
        resp.set_message(err.to_string().as_bytes().to_vec());
        resp
    }
}

impl From<crate::storage::Error> for RedisResponse {
    fn from(err: crate::storage::Error) -> Self {
        let mut resp = RedisResponse::new();
        resp.set_type(ContentType::Error);
        resp.set_message(err.to_string().as_bytes().to_vec());
        resp
    }
}

pub fn build_ok_response() -> RedisResponse {
    let mut resp = RedisResponse::new();
    resp.set_type(ContentType::Simple);
    resp.set_message("OK".as_bytes().to_vec());
    resp
}

pub fn build_error_response(value: Vec<u8>) -> RedisResponse {
    let mut resp = RedisResponse::new();
    resp.set_type(ContentType::Error);
    resp.set_message(value);
    resp
}

pub fn build_nil_response() -> RedisResponse {
    let mut resp = RedisResponse::new();
    resp.set_type(ContentType::Bulk);
    resp.set_is_null_content(true);
    resp
}

pub fn build_bulk_response(value: Vec<u8>) -> RedisResponse {
    let mut resp = RedisResponse::new();
    resp.set_type(ContentType::Bulk);
    resp.set_message(value);
    resp
}

pub fn build_int_response(value: i64) -> RedisResponse {
    let mut resp = RedisResponse::new();
    resp.set_type(ContentType::Integer);
    resp.set_message(value.to_string().as_bytes().to_vec());
    resp
}
