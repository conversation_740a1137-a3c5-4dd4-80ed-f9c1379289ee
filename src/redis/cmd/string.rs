use anyhow::Result;
use api_version::KvFormat;
use kvproto::kvrpcpb::{Context, RedisRequest, RedisResponse};
use tikv_kv::Engine;
use tikv_util::future::paired_future_callback;

use crate::{
    redis::{
        build_nil_response, build_ok_response, error::RedisError, meta::MetaData,
        parse_expire_flags, parser::CommandParser, CmdAttr, RedisType, build_bulk_response,
    },
    storage::{errors::extract_region_error, lock_manager::LockManager, Storage},
};

#[derive(Default, PartialEq, Eq)]
enum StringSetType {
    #[default]
    None,
    NX,
    XX,
}

pub struct CommandGet {
    key: Vec<u8>,
}

impl CommandGet {
    const ATTR: CmdAttr = CmdAttr::new("GET", 2);

    pub fn parse(req: &RedisRequest) -> Result<CommandGet, RedisError> {
        if !Self::ATTR.check_arity(req.get_args().len() as i32 + 2) {
            return Err(RedisError::InvalidArg);
        }

        Ok(CommandGet {
            key: req.get_key().to_vec(),
        })
    }

    pub async fn execute<E, L, F>(
        &self,
        ctx: Context,
        storage: Storage<E, L, F>,
    ) -> Result<RedisResponse>
    where
        E: Engine,
        L: LockManager,
        F: KvFormat,
    {
        let result = get_value_and_expire(ctx, storage, self.key.clone()).await?;
        if let Some((value, _)) = result {
            Ok(build_bulk_response(value))
        } else {
            Ok(build_nil_response())
        }
    }
}

pub struct CommandSet {
    key: Vec<u8>,
    value: Vec<u8>,
    expire: u64,
    get: bool,
    keep_ttl: bool,
    set_flag: StringSetType,
}

impl CommandSet {
    const ATTR: CmdAttr = CmdAttr::new("SET", -3);

    pub fn parse(req: &RedisRequest) -> Result<CommandSet, RedisError> {
        if !Self::ATTR.check_arity(req.get_args().len() as i32 + 2) {
            return Err(RedisError::InvalidArg);
        }

        let mut command = CommandSet {
            key: req.get_key().to_vec(),
            value: req.get_args()[0].to_vec(),
            expire: 0,
            get: false,
            keep_ttl: false,
            set_flag: Default::default(),
        };

        let mut parser = CommandParser::new(req.get_args().iter());
        parser.skip(1);

        let mut ttl_flag = String::new();
        let mut set_flag = String::new();
        while parser.good() {
            if let Ok(Some(v)) = parse_expire_flags(&mut parser, &mut ttl_flag) {
                command.expire = v;
            } else if parser.eat_eq_icase_flag("KEEPTTL", &mut ttl_flag) {
                command.keep_ttl = true;
            } else if parser.eat_eq_icase_flag("NX", &mut set_flag) {
                command.set_flag = StringSetType::NX;
            } else if parser.eat_eq_icase_flag("XX", &mut set_flag) {
                command.set_flag = StringSetType::XX;
            } else if parser.eat_eq_icase("GET") {
                command.get = true;
            } else {
                return Err(RedisError::InvalidArg);
            }
        }

        Ok(command)
    }

    pub async fn execute<E, L, F>(
        &mut self,
        ctx: Context,
        storage: Storage<E, L, F>,
    ) -> Result<RedisResponse>
    where
        E: Engine,
        L: LockManager,
        F: KvFormat,
    {
        // need read old value first
        if self.set_flag != StringSetType::None || self.get || self.keep_ttl {
            let storage = storage.clone();
            let old_value = get_value_and_expire(ctx.clone(), storage, self.key.clone()).await?;

            if (self.set_flag == StringSetType::NX && old_value.is_some())
                || (self.set_flag == StringSetType::XX && old_value.is_none())
            {
                return Ok(build_nil_response());
            }

            if self.keep_ttl && let Some((_, ttl)) = old_value {
                self.expire = ttl;
            }
        }

        let mut meta = MetaData::new(RedisType::String, false);
        meta.set_expire(self.expire);

        let (cb, future) = paired_future_callback();
        let result = storage.raw_put(
            ctx,
            "".to_string(),
            self.key.clone(),
            meta.encode(self.value.as_slice()),
            0,
            cb,
        );

        let resp = match result {
            Ok(_) => future.await?,
            Err(e) => Err(e),
        };

        let resp = if let Err(e) = resp {
            e.into()
        } else {
            build_ok_response()
        };

        Ok(resp)
    }
}

async fn get_value_and_expire<E, L, F>(
    ctx: Context,
    storage: Storage<E, L, F>,
    key: Vec<u8>,
) -> Result<Option<(Vec<u8>, u64)>>
where
    E: Engine,
    L: LockManager,
    F: KvFormat,
{
    let future = storage.raw_get(ctx, "".to_string(), key);
    let value = future.await;

    // TODO: 处理其他异常
    if let Some(err) = extract_region_error(&value) {
        return Err(anyhow::anyhow!("Region error: {:?}", err));
    }

    match value {
        Ok(Some(mut value)) => {
            let metadata = MetaData::parse(RedisType::String, &mut value)?;
            if metadata.is_expired() {
                Ok(None)
            } else {
                Ok(Some((value, metadata.get_expire())))
            }
        }
        Ok(None) => Ok(None),
        Err(_) => Ok(None),
    }
}
