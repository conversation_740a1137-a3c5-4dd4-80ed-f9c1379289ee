// Copyright 2016 TiKV Project Authors. Licensed under Apache-2.0.

pub(crate) mod metrics;
mod raft_client;

pub mod config;
pub mod debug;
mod engine_factory;
mod engine_factory_v2;
pub mod errors;
pub mod gc_worker;
pub mod load_statistics;
pub mod lock_manager;
pub mod node;
mod proxy;
pub mod raftkv;
mod reset_to_version;
pub mod resolve;
pub mod server;
pub mod service;
pub mod snap;
pub mod status_server;
pub mod tablet_snap;
pub mod transport;
pub mod ttl;

pub use engine_factory::{KvEngineFactory, KvEngineFactoryBuilder};

#[cfg(any(test, feature = "testexport"))]
pub use self::server::test_router::TestRaftStoreRouter;
pub use self::{
    config::{Config, ServerConfigManager, DEFAULT_CLUSTER_ID, DEFAULT_LISTENING_ADDR},
    errors::{Error, Result},
    metrics::{CONFIG_ROCKSDB_GAUGE, CPU_CORES_QUOTA_GAUGE, MEM_TRACE_SUM_GAUGE},
    node::{create_raft_storage, Node},
    proxy::{build_forward_option, get_target_address, Proxy},
    raft_client::{ConnectionBuilder, RaftClient},
    raftkv::RaftKv,
    resolve::{PdStoreAddrResolver, StoreAddrResolver},
    server::{Server, GRPC_THREAD_PREFIX},
    transport::ServerTransport,
};
